[gd_scene load_steps=2 format=3 uid="uid://bqxvn8y7qr8ys"]

[ext_resource type="Script" path="res://QuizGame.gd" id="1_1x8qr"]

[node name="QuizGame" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_1x8qr")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.2, 0.3, 0.5, 1)

[node name="QuestionContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = -50.0
grow_horizontal = 2
grow_vertical = 0

[node name="ScoreLabel" type="Label" parent="QuestionContainer"]
layout_mode = 2
text = "Score: 0 / 0"
horizontal_alignment = 1
vertical_alignment = 1

[node name="QuestionLabel" type="Label" parent="QuestionContainer"]
layout_mode = 2
text = "Question will appear here"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = 3

[node name="OptionsContainer" type="VBoxContainer" parent="QuestionContainer"]
layout_mode = 2
alignment = 1

[node name="NextButton" type="Button" parent="QuestionContainer"]
layout_mode = 2
text = "Next Question"
