# Godot Web Export - Cross Origin Isolation Fix

This document explains how to solve the Cross Origin Isolation and SharedArrayBuffer issues when running Godot web exports.

## The Problem

Modern browsers require specific HTTP headers to enable SharedArrayBuffer, which Godot's threading system needs:
- `Cross-Origin-Embedder-Policy: require-corp`
- `Cross-Origin-Opener-Policy: same-origin`

These headers cannot be set when opening HTML files directly (using `file://` protocol).

## Solutions

### Option 1: Use the Included Server Scripts (Recommended)

#### Python Server (Python 3 required)
```bash
python serve.py [port]
```
Default port is 8000. Then access: http://localhost:8000/whatif_template.html

#### Node.js Server (Node.js required)
```bash
node serve.js [port]
```
Default port is 8000. Then access: http://localhost:8000/whatif_template.html

### Option 2: Use Built-in Tools

#### Python (if you have Python installed)
```bash
# Python 3
python -m http.server 8000

# Python 2 (deprecated)
python -m SimpleHTTPServer 8000
```
**Note:** Built-in Python server doesn't add COI headers, so this may not work.

#### Node.js with npx
```bash
npx serve .
```
**Note:** The `serve` package may not add COI headers by default.

### Option 3: Use VS Code Live Server Extension

1. Install the "Live Server" extension in VS Code
2. Right-click on `whatif_template.html`
3. Select "Open with Live Server"

**Note:** You may need to configure the extension to add COI headers.

## What Was Fixed

1. **HTML File (`whatif_template.html`)**:
   - Added meta tags for Cross-Origin-Embedder-Policy and Cross-Origin-Opener-Policy
   - Enhanced error messages with helpful instructions

2. **Service Worker (`whatif_template.service.worker.js`)**:
   - Modified to add COI headers to cached responses
   - Ensures proper headers for .html, .js, and .wasm files

3. **Server Scripts**:
   - `serve.py`: Python-based server with COI headers
   - `serve.js`: Node.js-based server with COI headers

## Troubleshooting

### Still Getting COI Errors?
1. Make sure you're accessing via `http://localhost` not `file://`
2. Try clearing your browser cache
3. Check browser console for specific error messages
4. Ensure you're using a modern browser (Chrome 88+, Firefox 79+, Safari 15.2+)

### Port Already in Use?
```bash
# Try a different port
python serve.py 8080
# or
node serve.js 8080
```

### HTTPS Required?
Some browsers may require HTTPS for SharedArrayBuffer. You can:
1. Use localhost (usually exempt from HTTPS requirement)
2. Set up a local HTTPS server
3. Deploy to a proper web server with HTTPS

## Browser Compatibility

- **Chrome 88+**: Full support
- **Firefox 79+**: Full support  
- **Safari 15.2+**: Full support
- **Edge 88+**: Full support

Older browsers may not support SharedArrayBuffer or Cross-Origin Isolation.

## Production Deployment

For production deployment, ensure your web server sends these headers:
```
Cross-Origin-Embedder-Policy: require-corp
Cross-Origin-Opener-Policy: same-origin
Cross-Origin-Resource-Policy: cross-origin
```

Common web server configurations:

### Apache (.htaccess)
```apache
Header always set Cross-Origin-Embedder-Policy "require-corp"
Header always set Cross-Origin-Opener-Policy "same-origin"
Header always set Cross-Origin-Resource-Policy "cross-origin"
```

### Nginx
```nginx
add_header Cross-Origin-Embedder-Policy "require-corp" always;
add_header Cross-Origin-Opener-Policy "same-origin" always;
add_header Cross-Origin-Resource-Policy "cross-origin" always;
```
