#!/usr/bin/env node
/**
 * Simple HTTP server with Cross-Origin Isolation headers for Godot web exports.
 * This server adds the necessary headers to enable SharedArrayBuffer and threading.
 * 
 * Usage:
 *     node serve.js [port]
 *     
 * Default port is 8000.
 * Access your game at: http://localhost:8000/whatif_template.html
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// MIME types for common file extensions
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm',
    '.pck': 'application/octet-stream'
};

function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

function addCOIHeaders(res, filePath) {
    // Add Cross-Origin Isolation headers
    res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
    res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
    
    // Add CORP header for resources
    const ext = path.extname(filePath).toLowerCase();
    if (['.js', '.wasm', '.pck', '.html', '.css', '.png', '.jpg', '.svg'].includes(ext)) {
        res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
    }
    
    // Add cache control for development
    if (['.js', '.wasm', '.pck'].includes(ext)) {
        res.setHeader('Cache-Control', 'no-cache');
    }
}

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url);
    let pathname = parsedUrl.pathname;
    
    // Default to index.html or whatif_template.html
    if (pathname === '/') {
        if (fs.existsSync('whatif_template.html')) {
            pathname = '/whatif_template.html';
        } else if (fs.existsSync('index.html')) {
            pathname = '/index.html';
        }
    }
    
    const filePath = path.join(process.cwd(), pathname);
    
    // Security check - prevent directory traversal
    if (!filePath.startsWith(process.cwd())) {
        res.writeHead(403, { 'Content-Type': 'text/plain' });
        res.end('Forbidden');
        return;
    }
    
    fs.stat(filePath, (err, stats) => {
        if (err || !stats.isFile()) {
            res.writeHead(404, { 'Content-Type': 'text/plain' });
            res.end('File not found');
            return;
        }
        
        const mimeType = getMimeType(filePath);
        addCOIHeaders(res, filePath);
        res.setHeader('Content-Type', mimeType);
        
        const stream = fs.createReadStream(filePath);
        stream.pipe(res);
        
        stream.on('error', (streamErr) => {
            res.writeHead(500, { 'Content-Type': 'text/plain' });
            res.end('Internal Server Error');
        });
    });
});

// Get port from command line argument or use default
const port = process.argv[2] ? parseInt(process.argv[2]) : 8000;

if (isNaN(port)) {
    console.error(`Invalid port number: ${process.argv[2]}`);
    process.exit(1);
}

server.listen(port, () => {
    console.log('Starting server with Cross-Origin Isolation support...');
    console.log(`Serving directory: ${process.cwd()}`);
    console.log(`Server running at: http://localhost:${port}/`);
    console.log(`Access your game at: http://localhost:${port}/whatif_template.html`);
    console.log('Press Ctrl+C to stop the server');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`Port ${port} is already in use. Try a different port.`);
    } else {
        console.error('Server error:', err);
    }
    process.exit(1);
});

process.on('SIGINT', () => {
    console.log('\nServer stopped.');
    process.exit(0);
});
