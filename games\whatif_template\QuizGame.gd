extends Control

# Quiz Game Script
# Handles loading quiz data from JSON and managing quiz flow

@onready var background: ColorRect = $Background
@onready var question_container: VBoxContainer = $QuestionContainer
@onready var question_label: Label = $QuestionContainer/QuestionLabel
@onready var options_container: VBoxContainer = $QuestionContainer/OptionsContainer
@onready var score_label: Label = $QuestionContainer/ScoreLabel
@onready var next_button: Button = $QuestionContainer/NextButton

var quiz_data: Dictionary = {}
var current_question_index: int = 0
var score: int = 0
var selected_answer: int = -1

func _ready():
	print("Quiz Game starting...")

	# Load quiz data from JSON file
	load_quiz_data("res://sample_quiz.json")
	setup_ui()
	display_current_question()

func load_quiz_data(file_path: String):
	"""Load quiz questions from JSON file"""
	var file = FileAccess.open(file_path, FileAccess.READ)
	if file == null:
		print("Error: Could not open quiz file: ", file_path)
		# Create default quiz data if file doesn't exist
		create_default_quiz_data()
		return

	var json_string = file.get_as_text()
	file.close()

	var json = JSON.new()
	var parse_result = json.parse(json_string)

	if parse_result != OK:
		print("Error parsing JSON: ", json.get_error_message())
		create_default_quiz_data()
		return

	quiz_data = json.data

func create_default_quiz_data():
	"""Create default quiz data if JSON file is not found"""
	quiz_data = {
		"questions": [
			{
				"question": "What is the capital of France?",
				"options": ["London", "Berlin", "Paris", "Madrid"],
				"correct_answer": 2
			},
			{
				"question": "Which planet is known as the Red Planet?",
				"options": ["Venus", "Mars", "Jupiter", "Saturn"],
				"correct_answer": 1
			},
			{
				"question": "What is 15 + 27?",
				"options": ["40", "41", "42", "43"],
				"correct_answer": 2
			}
		]
	}

func setup_ui():
	"""Setup the user interface"""
	# Connect next button
	next_button.pressed.connect(_on_next_button_pressed)
	next_button.visible = false

	# Update score display
	update_score_display()

func display_current_question():
	"""Display the current question and its options"""
	if current_question_index >= quiz_data.questions.size():
		show_final_score()
		return

	var question_data = quiz_data.questions[current_question_index]
	question_label.text = question_data.question

	# Clear existing option buttons
	for child in options_container.get_children():
		if child is Button:
			child.queue_free()

	# Create option buttons
	for i in range(question_data.options.size()):
		var option_button = Button.new()
		option_button.text = question_data.options[i]
		option_button.custom_minimum_size = Vector2(200, 50)
		option_button.pressed.connect(_on_option_selected.bind(i))
		options_container.add_child(option_button)

	# Reset selection state
	selected_answer = -1
	next_button.visible = false

func _on_option_selected(option_index: int):
	"""Handle option selection"""
	selected_answer = option_index
	var question_data = quiz_data.questions[current_question_index]

	# Disable all option buttons and show correct answer
	for i in range(options_container.get_child_count()):
		var button = options_container.get_child(i)
		if button is Button:
			button.disabled = true

			# Color code the buttons
			if i == question_data.correct_answer:
				button.modulate = Color.GREEN  # Correct answer
			elif i == selected_answer and i != question_data.correct_answer:
				button.modulate = Color.RED    # Wrong selected answer

	# Update score if correct
	if selected_answer == question_data.correct_answer:
		score += 1
		update_score_display()

	# Show next button
	next_button.visible = true

func _on_next_button_pressed():
	"""Move to next question"""
	current_question_index += 1

	# Reset button colors
	for child in options_container.get_children():
		if child is Button:
			child.modulate = Color.WHITE
			child.disabled = false

	display_current_question()

func update_score_display():
	"""Update the score display"""
	score_label.text = "Score: %d / %d" % [score, quiz_data.questions.size()]

func show_final_score():
	"""Show final score and quiz completion"""
	question_label.text = "Quiz Complete!"

	# Clear option buttons
	for child in options_container.get_children():
		if child is Button:
			child.queue_free()

	# Show final score
	var final_score_label = Label.new()
	final_score_label.text = "Final Score: %d / %d" % [score, quiz_data.questions.size()]
	final_score_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	options_container.add_child(final_score_label)

	# Add restart button
	var restart_button = Button.new()
	restart_button.text = "Restart Quiz"
	restart_button.custom_minimum_size = Vector2(200, 50)
	restart_button.pressed.connect(_on_restart_quiz)
	options_container.add_child(restart_button)

	next_button.visible = false

func _on_restart_quiz():
	"""Restart the quiz"""
	current_question_index = 0
	score = 0
	selected_answer = -1
	update_score_display()
	display_current_question()

