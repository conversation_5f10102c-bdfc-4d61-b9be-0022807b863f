extends Control

# Simple Quiz Game - Minimal Version for Testing

func _ready():
	print("Simple Quiz Game started!")
	
	# Create a simple UI
	var vbox = VBoxContainer.new()
	add_child(vbox)
	
	# Center the container
	vbox.anchors_preset = Control.PRESET_CENTER
	vbox.position = Vector2(400, 300)
	
	# Add a title
	var title = Label.new()
	title.text = "Simple Quiz Game"
	title.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(title)
	
	# Add a question
	var question = Label.new()
	question.text = "What is 2 + 2?"
	question.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	vbox.add_child(question)
	
	# Add answer buttons
	var answers = ["3", "4", "5", "6"]
	for i in range(answers.size()):
		var button = Button.new()
		button.text = answers[i]
		button.custom_minimum_size = Vector2(200, 40)
		button.pressed.connect(_on_answer_selected.bind(i))
		vbox.add_child(button)

func _on_answer_selected(answer_index: int):
	print("Answer selected: ", answer_index)
	if answer_index == 1:  # Correct answer is "4"
		print("Correct!")
	else:
		print("Wrong!")
